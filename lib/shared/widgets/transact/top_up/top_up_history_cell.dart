import 'package:flutter/material.dart';
import 'package:wd/core/models/entities/top_up_record_entity.dart';
import 'package:wd/core/utils/clipboardTool.dart';
import 'package:wd/core/utils/screenUtil.dart';

class TopUpHistoryCell extends StatelessWidget {
  final TopUpRecord model;

  const TopUpHistoryCell({super.key, required this.model});

  static Widget buildOrderStatusWidget(context, {required int orderStatus}) {
    // orderStatus 订单状态（0：待审核，1：已通过，2：已取消, 3: 已拒绝）
    var title = "";
    var backgroundColor = Colors.transparent;
    var textColor = const Color(0xff38ED14);

    switch (orderStatus) {
      case 0:
        title = "待审核";
        backgroundColor = const Color(0xff838383).withOpacity(0.11);
        textColor = const Color(0xff838383);
        break;
      case 1:
        title = "已通过";
        backgroundColor = const Color(0xff38ED14).withOpacity(0.11);
        textColor = const Color(0xff38ED14);
        break;
      case 2:
        title = "已取消";
        backgroundColor = const Color(0xffec8c89).withOpacity(0.11);
        textColor = const Color(0xffec8c89);
        break;
      case 3:
        title = "已拒绝";
        backgroundColor = const Color(0xffdc4a3a).withOpacity(0.11);
        textColor = const Color(0xffdc4a3a);
        break;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.gw, vertical: 6.gw),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(6.gw),
      ),
      child: Text(
        title,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: textColor,
              fontSize: 14,
              fontWeight: FontWeight.w400,
            ),
      ),
    );
  }

  Widget _buildInfoRow(BuildContext context, String title, String content, {bool showBorder = true}) {
    return Container(
      decoration: BoxDecoration(
        border: showBorder
            ? const Border(
                bottom: BorderSide(
                  color: Color(0xFF212121),
                  width: 1,
                ),
              )
            : null,
      ),
      padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 16.gw),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                ),
          ),
          Text(
            content,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                ),
            textAlign: TextAlign.right,
          ),
        ],
      ),
    );
  }

  Widget _buildOrderRow(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 16.gw),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            "order N0-",
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                ),
          ),
          Row(
            children: [
              Text(
                model.transactionNo,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                    ),
              ),
              SizedBox(width: 8.gw),
              GestureDetector(
                onTap: () {
                  ClipboardTool.setData(model.transactionNo);
                },
                child: Icon(
                  Icons.copy,
                  color: Colors.white,
                  size: 16.gw,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 400.gw,
      margin: EdgeInsets.symmetric(horizontal: 15.gw, vertical: 8.gw),
      decoration: BoxDecoration(
        color: const Color(0xFF101010),
        borderRadius: BorderRadius.circular(12.gw),
      ),
      child: Column(
        children: [
          // Header section
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: const Color(0xFF212121),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12.gw),
                topRight: Radius.circular(12.gw),
              ),
            ),
            padding: EdgeInsets.all(16.gw),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "Deposit",
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.w500,
                      ),
                ),
                buildOrderStatusWidget(context, orderStatus: model.orderStatus),
              ],
            ),
          ),
          // Information rows
          _buildInfoRow(context, "Requested Amount", model.orderAmount.toString()),
          if (model.orderInitialAmount != model.finalAmount && model.orderStatus == 1)
            _buildInfoRow(context, "Credited Amount", model.finalAmount.toString()),
          _buildInfoRow(context, "Type", model.cashinWayName),
          _buildInfoRow(context, "Time", model.requestTime),
          _buildOrderRow(context),
        ],
      ),
    );
  }
}
