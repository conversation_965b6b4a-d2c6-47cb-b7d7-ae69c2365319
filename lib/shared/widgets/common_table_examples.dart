import 'package:flutter/material.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'common_table.dart';

/// Examples of how to use CommonTable with different styling options
class CommonTableExamples {
  
  /// Example 1: Basic table (like original commission records)
  static Widget basicTable() {
    return CommonTable(
      columns: [
        CommonTableColumn(title: 'Sub-agent', key: 'agent'),
        CommonTableColumn(title: 'Bet Amount', key: 'amount'),
        CommonTableColumn(title: 'Bet Comm', key: 'commission'),
        CommonTableColumn(title: 'Date', key: 'date'),
      ],
      data: [
        ['test***1', '9999.99', '9999.99', '2024-12-24'],
        ['test***2', '8999.99', '8999.99', '2024-12-24'],
        ['test***3', '7999.99', '7999.99', '2024-12-24'],
      ],
    );
  }

  /// Example 2: Table with yellow last column (like commission records)
  static Widget tableWithYellowLastColumn() {
    return CommonTable(
      columns: [
        CommonTableColumn(
          title: 'Sub-agent', 
          key: 'agent',
          width: 130.gw, // Wider first column
        ),
        CommonTableColumn(title: 'Bet Amount', key: 'amount'),
        CommonTableColumn(title: 'Bet Comm', key: 'commission'),
        CommonTableColumn(
          title: 'Date', 
          key: 'date',
          style: CommonTableColumnStyle.yellowText, // Yellow text
        ),
      ],
      data: [
        ['test***1', '9999.99', '9999.99', '2024-12-24'],
        ['test***2', '8999.99', '8999.99', '2024-12-24'],
        ['test***3', '7999.99', '7999.99', '2024-12-24'],
      ],
      width: 430.gw, // 130 + 100 + 100 + 100
    );
  }

  /// Example 3: Table with level badges (like team hierarchy)
  static Widget tableWithLevelBadges() {
    return CommonTable(
      columns: [
        CommonTableColumn(
          title: 'Sub-agent(999)', 
          key: 'agent',
          width: 130.gw,
        ),
        CommonTableColumn(
          title: 'Level', 
          key: 'level',
          width: 100.gw,
          style: CommonTableColumnStyle.levelBadge, // Badge style
        ),
        CommonTableColumn(
          title: 'Join Time', 
          key: 'joinTime',
          width: 100.gw,
        ),
        CommonTableColumn(
          title: 'Date', 
          key: 'date',
          width: 100.gw,
          style: CommonTableColumnStyle.yellowText, // Yellow text
        ),
      ],
      data: [
        ['test***1', 'Level 1', '18:09:45', '2024-12-24'],
        ['test***2', 'Level 2', '18:09:45', '2024-12-24'],
        ['test***3', 'Level 3', '18:09:45', '2024-12-24'],
      ],
      width: 430.gw,
    );
  }

  /// Example 4: Custom styled table
  static Widget customStyledTable() {
    return CommonTable(
      columns: [
        CommonTableColumn(
          title: 'Name', 
          key: 'name',
          width: 120.gw,
        ),
        CommonTableColumn(
          title: 'Status', 
          key: 'status',
          width: 100.gw,
          style: const CommonTableColumnStyle(
            showBadge: true,
            badgeColor: Color(0xFF4CAF50), // Green badge
            badgeTextColor: Colors.white,
            badgeRadius: 12,
          ),
        ),
        CommonTableColumn(
          title: 'Amount', 
          key: 'amount',
          width: 100.gw,
          style: const CommonTableColumnStyle(
            textColor: Color(0xFF2196F3), // Blue text
          ),
        ),
        CommonTableColumn(
          title: 'Date', 
          key: 'date',
          width: 110.gw,
          style: CommonTableColumnStyle.yellowText,
        ),
      ],
      data: [
        ['John Doe', 'Active', '1000.00', '2024-12-24'],
        ['Jane Smith', 'Pending', '2000.00', '2024-12-24'],
        ['Bob Johnson', 'Inactive', '500.00', '2024-12-24'],
      ],
      width: 430.gw,
    );
  }
}

/// Usage instructions and logic explanation
/// 
/// ## How to implement the styling logic:
/// 
/// ### 1. Last Column Yellow Text
/// ```dart
/// CommonTableColumn(
///   title: 'Date',
///   key: 'date',
///   style: CommonTableColumnStyle.yellowText,
/// )
/// ```
/// 
/// ### 2. Badge/Pill Style (like Level column)
/// ```dart
/// CommonTableColumn(
///   title: 'Level',
///   key: 'level', 
///   style: CommonTableColumnStyle.levelBadge,
/// )
/// ```
/// 
/// ### 3. Custom Colors
/// ```dart
/// CommonTableColumn(
///   title: 'Status',
///   key: 'status',
///   style: CommonTableColumnStyle(
///     textColor: Color(0xFF4CAF50),
///     backgroundColor: Color(0xFFE8F5E8),
///   ),
/// )
/// ```
/// 
/// ### 4. Dynamic Column Widths
/// ```dart
/// CommonTableColumn(
///   title: 'Sub-agent',
///   key: 'agent',
///   width: 130.gw, // Custom width
/// )
/// ```
/// 
/// ## Logic for Different Table Types:
/// 
/// 1. **Commission Records**: Yellow last column, wider first column
/// 2. **Team Hierarchy**: Level badges, yellow dates
/// 3. **Activity Records**: Standard styling
/// 4. **VIP Center**: Custom badge colors for levels
/// 
/// ## Reusing Existing Labels:
/// 
/// When the current page context already has matching labels, use them:
/// - Use `'subordinate'.tr()` instead of `'Sub-agent'` if it exists
/// - Use `'time'.tr()` instead of `'Date'` if it exists
/// - Use `'act_event_type'.tr()` instead of creating new translations
/// 
/// This ensures consistency and avoids duplicate translations.
